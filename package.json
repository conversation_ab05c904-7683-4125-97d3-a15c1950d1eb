{"name": "my-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --clear --port 8001", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android --port 8001", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "server": "json-server --watch server/db.json --port 3000", "android:clean": "cd android && .\\gradlew.bat clean", "build": "cd android && .\\gradlew.bat assembleRelease"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@reduxjs/toolkit": "^2.6.0", "@rneui/themed": "^4.0.0-rc.8", "@tanstack/react-query": "^5.72.2", "@twotalltotems/react-native-otp-input": "^1.3.11", "axios": "^1.7.9", "clsx": "^2.1.1", "eslint-config-expo": "~8.0.1", "eslint-config-prettier": "^10.0.2", "eslint-plugin-prettier": "^5.2.3", "expo": "~52.0.37", "expo-av": "~15.0.2", "expo-camera": "~16.0.17", "expo-checkbox": "~4.0.1", "expo-config": "^1.0.0", "expo-constants": "~17.0.7", "expo-device": "~7.0.2", "expo-document-picker": "~13.0.3", "expo-file-system": "~18.0.11", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-location": "~18.0.7", "expo-media-library": "~17.0.6", "expo-notifications": "~0.29.13", "expo-print": "~14.0.3", "expo-router": "~4.0.17", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-storage": "^51.0.8", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-web-browser": "~14.0.2", "nativewind": "^2.0.11", "prettier": "^3.5.3", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.54.2", "react-native": "0.76.7", "react-native-element-dropdown": "^2.12.4", "react-native-elements": "^3.4.3", "react-native-event-listeners": "^1.0.7", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-html-to-pdf": "^0.12.0", "react-native-maps": "1.18.0", "react-native-otp-inputs": "^7.4.0", "react-native-paper": "^5.13.1", "react-native-progress": "^5.0.1", "react-native-qrcode-svg": "^6.3.15", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-snap-carousel": "^3.9.1", "react-native-svg": "15.8.0", "react-native-svg-transformer": "^1.5.0", "react-native-text-input-otp": "^1.0.7", "react-native-textarea": "^1.0.4", "react-native-view-shot": "~4.0.3", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "react-native-wheel-picker-expo": "^0.5.4", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "start": "^5.1.0", "toggle-switch-react-native": "^3.3.0", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "eslint": "^8.57.0", "eslint-config-expo": "~8.0.1", "jest": "^29.2.1", "jest-expo": "~52.0.4", "json-server": "^0.17.4", "react-test-renderer": "18.3.1", "tailwindcss": "^3.3.2", "typescript": "^5.3.3"}, "private": true}